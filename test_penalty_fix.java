import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Simple test to verify penalty paid fix works correctly
 */
public class TestPenaltyFix {
    
    public static void main(String[] args) {
        System.out.println("Testing penalty paid fix...");
        
        // Test 1: Penalty with interest (should work before and after fix)
        testPenaltyWithInterest();
        
        // Test 2: Penalty without interest (should work after fix)
        testPenaltyWithoutInterest();
        
        // Test 3: Only penalty payment (should work after fix)
        testOnlyPenaltyPayment();
        
        System.out.println("All tests completed successfully!");
    }
    
    private static void testPenaltyWithInterest() {
        System.out.println("Test 1: Penalty with interest");
        
        BigDecimal repaymentAmount = BigDecimal.valueOf(1000.00);
        BigDecimal interestPaid = BigDecimal.valueOf(200.00);
        BigDecimal penaltyPaid = BigDecimal.valueOf(50.00);
        
        // Simulate the condition check from the fixed code
        boolean shouldCreateTransaction = repaymentAmount != null && 
                                        (interestPaid != null || penaltyPaid != null);
        
        if (shouldCreateTransaction) {
            System.out.println("✓ Transaction created with interest: " + interestPaid + " and penalty: " + penaltyPaid);
        } else {
            System.out.println("✗ Transaction NOT created");
        }
    }
    
    private static void testPenaltyWithoutInterest() {
        System.out.println("Test 2: Penalty without interest");
        
        BigDecimal repaymentAmount = BigDecimal.valueOf(1000.00);
        BigDecimal interestPaid = null; // This was causing the issue
        BigDecimal penaltyPaid = BigDecimal.valueOf(50.00);
        
        // Simulate the condition check from the fixed code
        boolean shouldCreateTransaction = repaymentAmount != null && 
                                        (interestPaid != null || penaltyPaid != null);
        
        if (shouldCreateTransaction) {
            System.out.println("✓ Transaction created with penalty: " + penaltyPaid + " (no interest)");
        } else {
            System.out.println("✗ Transaction NOT created - this would be the bug!");
        }
    }
    
    private static void testOnlyPenaltyPayment() {
        System.out.println("Test 3: Only penalty payment");
        
        BigDecimal repaymentAmount = BigDecimal.valueOf(50.00);
        BigDecimal interestPaid = null;
        BigDecimal penaltyPaid = BigDecimal.valueOf(50.00);
        
        // Simulate the condition check from the fixed code
        boolean shouldCreateTransaction = repaymentAmount != null && 
                                        (interestPaid != null || penaltyPaid != null);
        
        if (shouldCreateTransaction) {
            System.out.println("✓ Transaction created for penalty-only payment: " + penaltyPaid);
        } else {
            System.out.println("✗ Transaction NOT created - this would be the bug!");
        }
    }
}
