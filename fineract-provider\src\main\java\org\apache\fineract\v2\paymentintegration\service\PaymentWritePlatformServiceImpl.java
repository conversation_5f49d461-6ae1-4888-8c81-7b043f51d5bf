/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.v2.paymentintegration.service;

import static org.apache.fineract.v2.paymentintegration.constants.PaymentReservationConstants.ACCOUNT_ID_PARAM_NAME;
import static org.apache.fineract.v2.paymentintegration.constants.PaymentReservationConstants.DATE_FORMAT_PARAM_NAME;
import static org.apache.fineract.v2.paymentintegration.constants.PaymentReservationConstants.DEFAULT_DATE_FORMAT;
import static org.apache.fineract.v2.paymentintegration.constants.PaymentReservationConstants.DEFAULT_LOCALE;
import static org.apache.fineract.v2.paymentintegration.constants.PaymentReservationConstants.LOCALE_PARAM_NAME;
import static org.apache.fineract.v2.paymentintegration.constants.PaymentReservationConstants.NOTE_PARAM_NAME;
import static org.apache.fineract.v2.paymentintegration.constants.PaymentReservationConstants.PAYER_ID_PARAM_NAME;
import static org.apache.fineract.v2.paymentintegration.constants.PaymentReservationConstants.PAYMENT_TYPE_ID_PARAM_NAME;
import static org.apache.fineract.v2.paymentintegration.constants.PaymentReservationConstants.PERMISSION_CONFIRM_PAYMENT;
import static org.apache.fineract.v2.paymentintegration.constants.PaymentReservationConstants.PIN_PARAM_NAME;
import static org.apache.fineract.v2.paymentintegration.constants.PaymentReservationConstants.RECEIPT_NUMBER_PARAM_NAME;
import static org.apache.fineract.v2.paymentintegration.constants.PaymentReservationConstants.REFERENCE_NUMBER_PARAM_NAME;
import static org.apache.fineract.v2.paymentintegration.constants.PaymentReservationConstants.TRANSACTION_AMOUNT_PARAM_NAME;
import static org.apache.fineract.v2.paymentintegration.constants.PaymentReservationConstants.TRANSACTION_DATE_PARAM_NAME;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.fineract.infrastructure.core.api.JsonCommand;
import org.apache.fineract.infrastructure.core.data.ApiParameterError;
import org.apache.fineract.infrastructure.core.data.CommandProcessingResult;
import org.apache.fineract.infrastructure.core.data.CommandProcessingResultBuilder;
import org.apache.fineract.infrastructure.core.exception.PlatformApiDataValidationException;
import org.apache.fineract.infrastructure.core.exception.PlatformDataIntegrityException;
import org.apache.fineract.infrastructure.security.service.PlatformSecurityContext;
import org.apache.fineract.portfolio.account.PortfolioAccountType;
import org.apache.fineract.portfolio.client.domain.Client;
import org.apache.fineract.portfolio.client.exception.ClientNotActiveException;
import org.apache.fineract.portfolio.group.domain.Group;
import org.apache.fineract.portfolio.group.exception.GroupNotActiveException;
import org.apache.fineract.portfolio.loanaccount.domain.Loan;
import org.apache.fineract.portfolio.loanaccount.service.LoanAssembler;
import org.apache.fineract.portfolio.loanaccount.service.LoanWritePlatformService;
import org.apache.fineract.portfolio.paymenttype.data.PaymentTypeData;
import org.apache.fineract.portfolio.paymenttype.exception.PaymentTypeNotFoundException;
import org.apache.fineract.portfolio.paymenttype.service.PaymentTypeReadPlatformService;
import org.apache.fineract.portfolio.savings.domain.SavingsAccount;
import org.apache.fineract.portfolio.savings.domain.SavingsAccountAssembler;
import org.apache.fineract.portfolio.savings.service.SavingsAccountWritePlatformService;
import org.apache.fineract.v2.paymentintegration.client.ElPayIntegrationService;
import org.apache.fineract.v2.paymentintegration.client.exception.PaymentIntegrationException;
import org.apache.fineract.v2.paymentintegration.client.payload.PaymentResponse;
import org.apache.fineract.v2.paymentintegration.constants.PayerAccountTypeEnum;
import org.apache.fineract.v2.paymentintegration.constants.PaymentProvidersEnum;
import org.apache.fineract.v2.paymentintegration.constants.PaymentStatusEnum;
import org.apache.fineract.v2.paymentintegration.data.PaymentReservationDataValidator;
import org.apache.fineract.v2.paymentintegration.domain.PaymentReservation;
import org.apache.fineract.v2.paymentintegration.domain.PaymentReservationRepository;
import org.apache.fineract.v2.paymentintegration.exception.PaymentReservationInvalidException;
import org.apache.fineract.v2.paymentintegration.exception.PaymentReservationNotFoundException;
import org.apache.fineract.v2.paymentintegration.exception.PaymentTypeInvalidException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

@Service
public class PaymentWritePlatformServiceImpl implements PaymentWritePlatformService {

    public static final String EL_PAY_SUCCESS = "200";

    private final PlatformSecurityContext context;

    private final PaymentReservationDataValidator validator;

    private final PaymentReservationRepository paymentReservationRepository;

    private final ElPayIntegrationService elPayIntegrationService;

    private final SavingsAccountWritePlatformService savingsAccountWritePlatformService;

    private final LoanWritePlatformService loanWritePlatformService;

    private final SavingsAccountAssembler savingAccountAssembler;

    private final LoanAssembler loanAssembler;

    private final PaymentTypeReadPlatformService paymentTypeReadPlatformService;

    @Autowired
    public PaymentWritePlatformServiceImpl(PlatformSecurityContext context, PaymentReservationDataValidator validator,
            PaymentReservationRepository paymentReservationRepository, ElPayIntegrationService elPayIntegrationService,
            SavingsAccountWritePlatformService savingsAccountWritePlatformService, LoanWritePlatformService loanWritePlatformService,
            PaymentTypeReadPlatformService paymentTypeReadPlatformService, SavingsAccountAssembler savingAccountAssembler,
            LoanAssembler loanAssembler) {
        this.context = context;
        this.validator = validator;
        this.paymentReservationRepository = paymentReservationRepository;
        this.elPayIntegrationService = elPayIntegrationService;
        this.savingsAccountWritePlatformService = savingsAccountWritePlatformService;
        this.loanWritePlatformService = loanWritePlatformService;
        this.paymentTypeReadPlatformService = paymentTypeReadPlatformService;
        this.savingAccountAssembler = savingAccountAssembler;
        this.loanAssembler = loanAssembler;
    }

    @Override
    public CommandProcessingResult initiateDeposit(JsonCommand command) {
        return initiatePayment(PortfolioAccountType.SAVINGS, command);
    }

    @Override
    public CommandProcessingResult initiateLoanPayment(JsonCommand command) {
        return initiatePayment(PortfolioAccountType.LOAN, command);
    }

    @Override
    public CommandProcessingResult processPayment(JsonCommand command) {
        try {

            this.context.authenticatedUser();

            Map<String, Object> params = this.validator.validateForProcessPayment(command.json());

            String referenceNumber = (String) params.get(REFERENCE_NUMBER_PARAM_NAME);
            String pin = (String) params.get(PIN_PARAM_NAME);

            PaymentReservation paymentReservation = paymentReservationRepository.findByReferenceNumber(referenceNumber)
                    .orElseThrow(() -> new PaymentReservationNotFoundException(referenceNumber));

            PaymentStatusEnum paymentStatus = PaymentStatusEnum.fromInt(paymentReservation.getPaymentStatus());

            if (!paymentStatus.isPending()) throw new PaymentReservationInvalidException(referenceNumber, paymentStatus.name());

            PaymentResponse response;
            boolean isLoan = false;

            if (PortfolioAccountType.fromInt(paymentReservation.getAccountType()).isSavingsAccount()) {
                SavingsAccount account = savingAccountAssembler.assembleFrom(paymentReservation.getAccountId());
                validateSavingAccount(account);
            } else {
                Loan loan = loanAssembler.assembleFrom(paymentReservation.getAccountId());
                validateLoanAccount(loan);
                isLoan = true;
            }

            PaymentTypeData paymentTypeData = getPaymentType((long) paymentReservation.getPaymentTypeId());

            PaymentProvidersEnum paymentTypeEnum = PaymentProvidersEnum.valueOf(paymentTypeData.getName());

            if (paymentTypeEnum.isAmole())
                response = elPayIntegrationService.processPayment(referenceNumber, pin);
            else
                response = elPayIntegrationService.processPayment(referenceNumber);

            if (!response.getCode().equals(EL_PAY_SUCCESS)) {
                paymentReservation.failed(response.getMessage());
                this.paymentReservationRepository.saveAndFlush(paymentReservation);
                throw new PaymentIntegrationException("Error while processing el-pay payment " + response.getMessage());
            }

            CommandProcessingResult result;

            if (!isLoan)
                result = deposit(command, paymentReservation);
            else
                result = payLoan(command, paymentReservation);

            paymentReservation.paid(result.resourceId(), null, null);

            paymentReservationRepository.save(paymentReservation);

            return result;

        } catch (final JpaSystemException | DataIntegrityViolationException dve) {
            throw new PlatformDataIntegrityException("error.msg.paymentreservation.unknown.data.integrity.issue",
                    "Unknown data integrity issue with resource.", dve);
        }
    }

    @Override
    public CommandProcessingResult confirmPayment(JsonCommand command) {
        try {

            this.context.authenticatedUser().validateHasPermissionTo(PERMISSION_CONFIRM_PAYMENT);

            Map<String, Object> params = this.validator.validateForProcessPayment(command.json());

            String referenceNumber = (String) params.get(REFERENCE_NUMBER_PARAM_NAME);

            PaymentReservation paymentReservation = paymentReservationRepository.findByReferenceNumber(referenceNumber)
                    .orElseThrow(() -> new PaymentReservationNotFoundException(referenceNumber));

            PaymentStatusEnum paymentStatus = PaymentStatusEnum.fromInt(paymentReservation.getPaymentStatus());

            if (!paymentStatus.isPending()) throw new PaymentReservationInvalidException(referenceNumber, paymentStatus.name());

            CommandProcessingResult result;

            if (PortfolioAccountType.fromInt(paymentReservation.getAccountType()).isLoanAccount()) {
                result = payLoan(command, paymentReservation);
            } else {
                result = deposit(command, paymentReservation);
            }

            paymentReservation.paid(result.resourceId(), null, null);

            paymentReservationRepository.save(paymentReservation);

            return result;

        } catch (final JpaSystemException | DataIntegrityViolationException dve) {
            throw new PlatformDataIntegrityException("error.msg.paymentreservation.unknown.data.integrity.issue",
                    "Unknown data integrity issue with resource.", dve);
        }
    }

    private CommandProcessingResult initiatePayment(PortfolioAccountType accountType, JsonCommand command) {
        try {

            this.context.authenticatedUser();

            Map<String, Object> params = this.validator.validateForInitiatePayment(command.json());

            Long paymentTypeId = Long.valueOf(String.valueOf(params.get(PAYMENT_TYPE_ID_PARAM_NAME)));
            Long accountId = Long.valueOf(String.valueOf(params.get(ACCOUNT_ID_PARAM_NAME)));
            BigDecimal transactionAmount = (BigDecimal) params.get(TRANSACTION_AMOUNT_PARAM_NAME);
            String payerId = (String) params.get(PAYER_ID_PARAM_NAME);
            String note = (String) params.get(NOTE_PARAM_NAME);

            Client client;
            Long clientId = null;

            if (accountType.isSavingsAccount()) {
                SavingsAccount account = savingAccountAssembler.assembleFrom(accountId);
                validateSavingAccount(account);
                client = account.getClient();
            } else {
                Loan loan = loanAssembler.assembleFrom(accountId);
                client = loan.getClient();
            }

            if (client != null) {
                if (payerId == null) payerId = client.getMobileNo();

                clientId = client.getId();
            }

            PaymentTypeData paymentTypeData = getPaymentType(paymentTypeId);

            if (paymentTypeData == null || !paymentTypeData.getActive()) throw new PaymentTypeInvalidException(paymentTypeId);

            PaymentProvidersEnum paymentTypeEnum = PaymentProvidersEnum.valueOf(paymentTypeData.getName());

            PaymentReservation paymentReservation = PaymentReservation.createNew(transactionAmount, paymentTypeEnum.getValue(), clientId,
                    accountId, accountType.getValue(), payerId, note);

            this.paymentReservationRepository.saveAndFlush(paymentReservation);

            PaymentResponse response = elPayIntegrationService.initiatePayment(paymentTypeEnum.name(),
                    PayerAccountTypeEnum.PHONE_NUMBER.name(), payerId, note, transactionAmount, paymentReservation.getReferenceNumber());

            if (!response.getCode().equals(EL_PAY_SUCCESS)) {
                paymentReservation.failed(response.getMessage());
                this.paymentReservationRepository.saveAndFlush(paymentReservation);
                throw new PaymentIntegrationException("Error while initiating el-pay payment " + response.getMessage());
            }

            return new CommandProcessingResultBuilder().withResourceIdAsString(paymentReservation.getReferenceNumber()).build();

        } catch (final JpaSystemException | DataIntegrityViolationException dve) {
            throw new PlatformDataIntegrityException("error.msg.paymentreservation.unknown.data.integrity.issue",
                    "Unknown data integrity issue with resource.", dve);
        }
    }

    private CommandProcessingResult deposit(JsonCommand command, PaymentReservation reservation) {

        JsonCommand newCommand = getJsonCommand(command, reservation, false);

        return savingsAccountWritePlatformService.deposit(reservation.getAccountId(), newCommand);
    }

    private CommandProcessingResult payLoan(JsonCommand command, PaymentReservation reservation) {

        JsonCommand newCommand = getJsonCommand(command, reservation, true);

        return loanWritePlatformService.makeLoanRepayment(reservation.getAccountId(), newCommand, false,false);
    }

    private void validateLoanAccount(Loan loan) {

        final Client client = loan.getClient();

        if (client != null && client.isNotActive()) throw new ClientNotActiveException(client.getId());

        final Group group = loan.group();

        if (group != null && group.isNotActive()) throw new GroupNotActiveException(group.getId());

        loanWritePlatformService.validateClientContribution(loan);

    }

    private void validateSavingAccount(final SavingsAccount account) {

        final Client client = account.getClient();

        if (account.isNotActive()) {
            final String defaultUserMessage = "Transaction is not allowed. Account is not active.";
            final ApiParameterError error = ApiParameterError.parameterError("error.msg.savingsAccount.transaction.account.is.not.active",
                    defaultUserMessage, "accountId", account.getId());

            final List<ApiParameterError> dataValidationErrors = new ArrayList<>();
            dataValidationErrors.add(error);

            throw new PlatformApiDataValidationException(dataValidationErrors);
        }

        if (client != null && client.isNotActive()) throw new ClientNotActiveException(client.getId());

        final Group group = account.group();

        if (group != null && group.isNotActive()) throw new GroupNotActiveException(group.getId());
    }

    private JsonCommand getJsonCommand(JsonCommand command, PaymentReservation reservation, boolean isLoanAccount) {

        Gson gson = new Gson();

        JsonObject jsonObject = new JsonObject();

        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);

        jsonObject.addProperty(LOCALE_PARAM_NAME, DEFAULT_LOCALE);
        jsonObject.addProperty(DATE_FORMAT_PARAM_NAME, DEFAULT_DATE_FORMAT);
        jsonObject.addProperty(TRANSACTION_DATE_PARAM_NAME, LocalDate.now(ZoneId.systemDefault()).format(dateFormatter));
        jsonObject.addProperty(TRANSACTION_AMOUNT_PARAM_NAME, reservation.getTransactionAmount());
        jsonObject.addProperty(PAYMENT_TYPE_ID_PARAM_NAME, reservation.getPaymentTypeId());
        jsonObject.addProperty(RECEIPT_NUMBER_PARAM_NAME, reservation.getThirdPartyReceiptNumber());

        if (isLoanAccount) jsonObject.addProperty(NOTE_PARAM_NAME, reservation.getNote());

        JsonElement element = gson.fromJson(jsonObject.toString(), JsonElement.class);

        return JsonCommand.fromExistingCommand(command, element);
    }

    private PaymentTypeData getPaymentType(Long paymentTypeId) {

        try {

            return paymentTypeReadPlatformService.retrieveOne(paymentTypeId);

        } catch (EmptyResultDataAccessException ex) {

            throw new PaymentTypeNotFoundException(paymentTypeId);
        }
    }
}
