/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.portfolio.loanaccount.handler;

import org.apache.fineract.commands.annotation.CommandType;
import org.apache.fineract.commands.handler.NewCommandSourceHandler;
import org.apache.fineract.infrastructure.core.api.JsonCommand;
import org.apache.fineract.infrastructure.core.data.CommandProcessingResult;
import org.apache.fineract.portfolio.loanaccount.service.LoanWritePlatformService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@CommandType(entity = "LOAN", action = "REPAYMENT")
public class LoanRepaymentCommandHandler implements NewCommandSourceHandler {

    private final LoanWritePlatformService writePlatformService;

    @Autowired
    public LoanRepaymentCommandHandler(final LoanWritePlatformService writePlatformService) {
        this.writePlatformService = writePlatformService;
    }

    @Transactional
    @Override
    public CommandProcessingResult processCommand(final JsonCommand command) {
        boolean isRecoveryRepayment = false;

        // Check if this transaction is from bulk loan account import
        // This flag is set by LoanImportHandler to distinguish bulk loan import from regular repayments
        boolean isImported = command.booleanPrimitiveValueOfParameterNamed("isBulkLoanAccountImport");

        // Check if reschedule state should be preserved during import processing
        // This flag ensures that existing reschedule information is not affected by import operations
        boolean preserveRescheduleState = command.booleanPrimitiveValueOfParameterNamed("preserveRescheduleState");

        //second
        if(command.actionName().contains("PREPAY")){

            return this.writePlatformService.makeLoanRepaymentPrePay(command.getLoanId(), command, isRecoveryRepayment);
        }else{

            return this.writePlatformService.makeLoanRepayment(command.getLoanId(), command, isRecoveryRepayment, isImported, preserveRescheduleState);
        }

    }
}
