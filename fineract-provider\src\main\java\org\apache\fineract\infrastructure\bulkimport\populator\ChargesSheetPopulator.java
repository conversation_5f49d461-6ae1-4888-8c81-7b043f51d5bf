/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.infrastructure.bulkimport.populator;

import org.apache.fineract.infrastructure.bulkimport.constants.TemplatePopulateImportConstants;

import org.apache.fineract.portfolio.charge.data.ChargeData;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.ArrayList;
import java.util.List;

public class ChargesSheetPopulator extends AbstractWorkbookPopulator {

    private List<ChargeData> charges;

    private static final int ID_COL = 0;
    private static final int CHARGE_NAME_COL = 1;

    public ChargesSheetPopulator(final List<ChargeData> charges) {
        this.charges = charges;
    }

    @Override
    public void populate(final Workbook workbook, String dateFormat) {
        int rowIndex = 1;
        Sheet chargeSheet = workbook.createSheet(TemplatePopulateImportConstants.CHARGES_SHEET_NAME);
        setLayout(chargeSheet);
        populateCharges(chargeSheet, rowIndex);
        chargeSheet.protectSheet("");
    }

    private void populateCharges(Sheet officeSheet, int rowIndex) {
        for (ChargeData charge : charges) {
            Row row = officeSheet.createRow(rowIndex);
            writeLong(ID_COL, row, charge.getId());
            writeString(CHARGE_NAME_COL, row, charge.name().trim().replaceAll("[ )(]", "_"));
            rowIndex++;
        }
    }

    private void setLayout(Sheet worksheet) {
        worksheet.setColumnWidth(ID_COL, TemplatePopulateImportConstants.SMALL_COL_SIZE);
        worksheet.setColumnWidth(CHARGE_NAME_COL, TemplatePopulateImportConstants.MEDIUM_COL_SIZE);
        Row rowHeader = worksheet.createRow(TemplatePopulateImportConstants.ROWHEADER_INDEX);
        rowHeader.setHeight(TemplatePopulateImportConstants.ROW_HEADER_HEIGHT);
        writeString(ID_COL, rowHeader, "ID");
        writeString(CHARGE_NAME_COL, rowHeader, "Name");
    }

    public List<ChargeData> getCharges() {
        return charges;
    }

    public List<String> getChargesNames() {
        List<String> chargeNames = new ArrayList<>();
        for (ChargeData charge : charges) {
            chargeNames.add(charge.name());
        }
        return chargeNames;
    }

}
