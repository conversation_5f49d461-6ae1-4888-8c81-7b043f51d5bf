/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.v2.notification.service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.fineract.portfolio.loanaccount.domain.Loan;
import org.apache.fineract.portfolio.loanaccount.domain.LoanRepository;
import org.apache.fineract.v2.notification.domain.Notification;
import org.apache.fineract.v2.notification.domain.V2NotificationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class NotificationServiceImpl implements NotificationService {

    private V2NotificationRepository notificationRepository;
    private LoanRepository loanRepository;

    @Autowired
    public NotificationServiceImpl(V2NotificationRepository notificationRepository, LoanRepository loanRepository) {
        this.notificationRepository = notificationRepository;
        this.loanRepository = loanRepository;
    }

    @Override
    public List<Notification> retrieveAllNotifications() {
        return null;
    }

    @Override
    public void insertNotification(Notification notification) {
        notificationRepository.save(notification);
    }

    @Override
    public long GetUserFromLoan(long loanId) {
        Loan loan = loanRepository.findById(loanId).get();
        if (loan != null) return loan.client().getId();
        return 0;
    }

    @Override
    public List<Notification> getNotificationByClientId(long clientId) {
        return notificationRepository.findAll().stream().filter(notification -> notification.getBeneficiaryUser() == clientId)
                .collect(Collectors.toList());
    }

    @Override
    public void insertNotification(String type, String message, Double transactionAmount, long beneficiaryUser, long actionUser) {
        Notification notification = new Notification(type, message, LocalDate.now(ZoneId.systemDefault()), transactionAmount,
                beneficiaryUser, actionUser, 0, 0);
        notificationRepository.save(notification);
    }
}
