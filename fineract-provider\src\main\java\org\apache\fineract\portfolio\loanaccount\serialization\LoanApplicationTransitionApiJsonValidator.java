/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.portfolio.loanaccount.serialization;

import com.google.gson.JsonElement;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.apache.fineract.infrastructure.core.data.ApiParameterError;
import org.apache.fineract.infrastructure.core.data.DataValidatorBuilder;
import org.apache.fineract.infrastructure.core.exception.InvalidJsonException;
import org.apache.fineract.infrastructure.core.exception.PlatformApiDataValidationException;
import org.apache.fineract.infrastructure.core.serialization.FromJsonHelper;
import org.apache.fineract.portfolio.loanaccount.api.LoanApiConstants;
import org.apache.fineract.portfolio.loanaccount.domain.Loan;
import org.apache.fineract.portfolio.loanaccount.exception.LoanApplicationDateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public final class LoanApplicationTransitionApiJsonValidator {

    private final FromJsonHelper fromApiJsonHelper;

    @Autowired
    public LoanApplicationTransitionApiJsonValidator(final FromJsonHelper fromApiJsonHelper) {
        this.fromApiJsonHelper = fromApiJsonHelper;
    }

    private void throwExceptionIfValidationWarningsExist(final List<ApiParameterError> dataValidationErrors) {
        if (!dataValidationErrors.isEmpty()) {
            throw new PlatformApiDataValidationException("validation.msg.validation.errors.exist", "Validation errors exist.",
                    dataValidationErrors);
        }
    }

    public void validateApproval(Loan loan, final String json) {

        if (StringUtils.isBlank(json)) {
            throw new InvalidJsonException();
        }

        final Set<String> disbursementParameters = new HashSet<>(Arrays.asList(LoanApiConstants.loanIdTobeApproved,
                LoanApiConstants.approvedLoanAmountParameterName, LoanApiConstants.approvedOnDateParameterName,
                LoanApiConstants.noteParameterName, LoanApiConstants.localeParameterName, LoanApiConstants.dateFormatParameterName,
                LoanApiConstants.disbursementDataParameterName, LoanApiConstants.disbursementDateParameterName,
                LoanApiConstants.principalContributionParameterName));

        final Type typeOfMap = new TypeToken<Map<String, Object>>() {}.getType();
        this.fromApiJsonHelper.checkForUnsupportedParameters(typeOfMap, json, disbursementParameters);

        final List<ApiParameterError> dataValidationErrors = new ArrayList<>();
        final DataValidatorBuilder baseDataValidator = new DataValidatorBuilder(dataValidationErrors).resource("loanapplication");

        final JsonElement element = this.fromApiJsonHelper.parse(json);

        final BigDecimal principal = this.fromApiJsonHelper
                .extractBigDecimalWithLocaleNamed(LoanApiConstants.approvedLoanAmountParameterName, element);
        baseDataValidator.reset().parameter(LoanApiConstants.approvedLoanAmountParameterName).value(principal).ignoreIfNull()
                .positiveAmount();
/**when isClientContributionEnabled is true the function will fail. I can find principalContributionParameterName on the element */
        final boolean isClientContributionEnabled = loan.getLoanProduct().isClientContributionEnabled();
        if (isClientContributionEnabled) {
            final BigDecimal principalContribution = this.fromApiJsonHelper
                    .extractBigDecimalWithLocaleNamed(LoanApiConstants.principalContributionParameterName, element);
            baseDataValidator.reset().parameter(LoanApiConstants.principalContributionParameterName).value(principalContribution).notNull()
                    .positiveAmount();
        }

        final LocalDate approvedOnDate = this.fromApiJsonHelper.extractLocalDateNamed(LoanApiConstants.approvedOnDateParameterName,
                element);
        baseDataValidator.reset().parameter(LoanApiConstants.approvedOnDateParameterName).value(approvedOnDate).notNull();

        final LocalDate expectedDisbursementDate = this.fromApiJsonHelper
                .extractLocalDateNamed(LoanApiConstants.disbursementDateParameterName, element);
        baseDataValidator.reset().parameter(LoanApiConstants.disbursementDateParameterName).value(expectedDisbursementDate).ignoreIfNull();

        final String note = this.fromApiJsonHelper.extractStringNamed(LoanApiConstants.noteParameterName, element);
        baseDataValidator.reset().parameter(LoanApiConstants.noteParameterName).value(note).notExceedingLengthOf(1000);

        throwExceptionIfValidationWarningsExist(dataValidationErrors);
    }

    public void validateClientContributionLimit(final Loan loan, final BigDecimal principalClientContribution,
            final BigDecimal approvedLoanAmount) {
        final Integer defaultPercentage = loan.getLoanProduct().getDefaultClientContributionPercentage() != null
                ? loan.getLoanProduct().getDefaultClientContributionPercentage()
                : 20;
        final Integer minimumPercentage = loan.getLoanProduct().getMinimumClientContributionPercentage() != null
                ? loan.getLoanProduct().getMinimumClientContributionPercentage()
                : 5;

        final BigDecimal minimumExpectedClientContributionAmount = approvedLoanAmount
                .multiply(BigDecimal.valueOf((double) minimumPercentage / 100));

        if (principalClientContribution.compareTo(minimumExpectedClientContributionAmount) < 0) {
            String errorMessage = "Expected client contribution amount " + principalClientContribution
                    + " is lesser than the minimum allowed " + minimumExpectedClientContributionAmount;
            throw new LoanApplicationDateException("contribution.amount.less.than.expected", errorMessage, principalClientContribution,
                    minimumExpectedClientContributionAmount);
        }

        if (principalClientContribution.compareTo(approvedLoanAmount) > 0) {
            String errorMessage = "Expected client contribution amount " + principalClientContribution
                    + " is greater than the principal allowed " + loan.getProposedPrincipal();
            throw new LoanApplicationDateException("contribution.amount.exceeds.than.expected", errorMessage, principalClientContribution,
                    loan.getProposedPrincipal());
        }
    }

    public void validateRejection(final String json) {

        if (StringUtils.isBlank(json)) {
            throw new InvalidJsonException();
        }

        final Set<String> disbursementParameters = new HashSet<>(Arrays.asList(LoanApiConstants.rejectedOnDateParameterName,
                LoanApiConstants.noteParameterName, LoanApiConstants.localeParameterName, LoanApiConstants.dateFormatParameterName));

        final Type typeOfMap = new TypeToken<Map<String, Object>>() {}.getType();
        this.fromApiJsonHelper.checkForUnsupportedParameters(typeOfMap, json, disbursementParameters);

        final List<ApiParameterError> dataValidationErrors = new ArrayList<>();
        final DataValidatorBuilder baseDataValidator = new DataValidatorBuilder(dataValidationErrors).resource("loanapplication");

        final JsonElement element = this.fromApiJsonHelper.parse(json);
        final LocalDate rejectedOnDate = this.fromApiJsonHelper.extractLocalDateNamed(LoanApiConstants.rejectedOnDateParameterName,
                element);
        baseDataValidator.reset().parameter(LoanApiConstants.rejectedOnDateParameterName).value(rejectedOnDate).notNull();

        final String note = this.fromApiJsonHelper.extractStringNamed(LoanApiConstants.noteParameterName, element);
        baseDataValidator.reset().parameter(LoanApiConstants.noteParameterName).value(note).notExceedingLengthOf(1000);

        throwExceptionIfValidationWarningsExist(dataValidationErrors);
    }

    public void validateApplicantWithdrawal(final String json) {

        if (StringUtils.isBlank(json)) {
            throw new InvalidJsonException();
        }

        final Set<String> disbursementParameters = new HashSet<>(Arrays.asList(LoanApiConstants.withdrawnOnDateParameterName,
                LoanApiConstants.noteParameterName, LoanApiConstants.localeParameterName, LoanApiConstants.dateFormatParameterName));

        final Type typeOfMap = new TypeToken<Map<String, Object>>() {}.getType();
        this.fromApiJsonHelper.checkForUnsupportedParameters(typeOfMap, json, disbursementParameters);

        final List<ApiParameterError> dataValidationErrors = new ArrayList<>();
        final DataValidatorBuilder baseDataValidator = new DataValidatorBuilder(dataValidationErrors).resource("loanapplication");

        final JsonElement element = this.fromApiJsonHelper.parse(json);
        final LocalDate withdrawnOnDate = this.fromApiJsonHelper.extractLocalDateNamed(LoanApiConstants.withdrawnOnDateParameterName,
                element);
        baseDataValidator.reset().parameter(LoanApiConstants.withdrawnOnDateParameterName).value(withdrawnOnDate).notNull();

        final String note = this.fromApiJsonHelper.extractStringNamed(LoanApiConstants.noteParameterName, element);
        baseDataValidator.reset().parameter(LoanApiConstants.noteParameterName).value(note).notExceedingLengthOf(1000);

        throwExceptionIfValidationWarningsExist(dataValidationErrors);
    }

    public void validateClientLoanContribution(final Loan loan, final String json) {

        if (StringUtils.isBlank(json)) {
            throw new InvalidJsonException();
        }

        final Set<String> contributionParameters = new HashSet<>(Arrays.asList(LoanApiConstants.localeParameterName,
                LoanApiConstants.noteParameterName, LoanApiConstants.principalContributionDateParameterName,
                LoanApiConstants.approvedLoanAmountParameterName, LoanApiConstants.expectedContributionDateParameterName,
                LoanApiConstants.principalContributionParameterName, LoanApiConstants.remainingContributionDateParameterName));

        final Type typeOfMap = new TypeToken<Map<String, Object>>() {}.getType();
        this.fromApiJsonHelper.checkForUnsupportedParameters(typeOfMap, json, contributionParameters);

        final List<ApiParameterError> dataValidationErrors = new ArrayList<>();
        final DataValidatorBuilder baseDataValidator = new DataValidatorBuilder(dataValidationErrors).resource("loanapplication");

        final JsonElement element = this.fromApiJsonHelper.parse(json);

        final BigDecimal remainingPrincipalContribution = this.fromApiJsonHelper
                .extractBigDecimalWithLocaleNamed(LoanApiConstants.remainingContributionDateParameterName, element);
        baseDataValidator.reset().parameter(LoanApiConstants.remainingContributionDateParameterName).value(remainingPrincipalContribution)
                .notNull().positiveAmount();

        if (loan.getApprovedPrincipal().compareTo(remainingPrincipalContribution) <= 0) {
            String errorMessage = "Remaining client contribution amount " + remainingPrincipalContribution
                    + " is greater than approved loan amount " + loan.getApprovedPrincipal();
            throw new LoanApplicationDateException("remaining.contribution.amount.greater.than.approved", errorMessage,
                    remainingPrincipalContribution, loan.getApprovedPrincipal());
        }

        long elapsedDays = ChronoUnit.DAYS.between(LocalDate.now(ZoneId.systemDefault()), loan.getClientContributionPaidDate());
        if (elapsedDays > 90) {
            String errorMessage = "contribution date has passed, client contribution should have been done with 90 days of initial contribution ";
            throw new LoanApplicationDateException("contribution.date.has.passed", errorMessage, elapsedDays);
        }

        throwExceptionIfValidationWarningsExist(dataValidationErrors);
    }

}
