/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.portfolio.loanproduct.domain;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.fineract.organisation.monetary.domain.Money;
import org.apache.fineract.portfolio.loanproduct.LoanProductConstants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Test class for LoanProduct borrower cycle variations functionality.
 * This test specifically verifies the fix for the bug where REPAYMENT case
 * incorrectly set MAX_INTEREST_RATE_PER_PERIOD instead of NUMBER_OF_REPAYMENTS.
 */
public class LoanProductBorrowerCycleVariationsTest {

    private LoanProduct loanProduct;
    private LoanProductRelatedDetail loanProductRelatedDetail;

    @BeforeEach
    public void setUp() {
        // Mock the LoanProductRelatedDetail
        loanProductRelatedDetail = mock(LoanProductRelatedDetail.class);
        Money principal = mock(Money.class);
        when(principal.getAmount()).thenReturn(BigDecimal.valueOf(10000));
        when(loanProductRelatedDetail.getPrincipal()).thenReturn(principal);
        when(loanProductRelatedDetail.getNominalInterestRatePerPeriod()).thenReturn(BigDecimal.valueOf(12.0));
        when(loanProductRelatedDetail.getNumberOfRepayments()).thenReturn(12);

        // Create a LoanProduct instance
        loanProduct = new LoanProduct();
        // Use reflection to set the private field
        try {
            java.lang.reflect.Field field = LoanProduct.class.getDeclaredField("loanProductRelatedDetail");
            field.setAccessible(true);
            field.set(loanProduct, loanProductRelatedDetail);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set loanProductRelatedDetail", e);
        }
    }

    @Test
    public void testFetchBorrowerCycleVariationsForCycleNumber_RepaymentCaseCorrectlySetNumberOfRepayments() {
        // Create borrower cycle variations
        List<LoanProductBorrowerCycleVariations> borrowerCycleVariations = new ArrayList<>();
        
        // Add a REPAYMENT variation for cycle 1
        LoanProductBorrowerCycleVariations repaymentVariation = new LoanProductBorrowerCycleVariations(
            1, // borrower cycle number
            LoanProductParamType.REPAYMENT.getValue(), // param type
            LoanProductValueConditionType.EQUAL.getValue(), // value condition type
            BigDecimal.valueOf(6), // min value (min repayments)
            BigDecimal.valueOf(24), // max value (max repayments)
            BigDecimal.valueOf(12) // default value (default repayments)
        );
        borrowerCycleVariations.add(repaymentVariation);

        // Set the borrower cycle variations using reflection
        try {
            java.lang.reflect.Field field = LoanProduct.class.getDeclaredField("borrowerCycleVariations");
            field.setAccessible(true);
            field.set(loanProduct, borrowerCycleVariations);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set borrowerCycleVariations", e);
        }

        // Test the method
        Map<String, BigDecimal> result = loanProduct.fetchBorrowerCycleVariationsForCycleNumber(1);

        // Verify the results
        assertNotNull(result);
        
        // Verify that NUMBER_OF_REPAYMENTS is set correctly (not MAX_INTEREST_RATE_PER_PERIOD)
        assertEquals(BigDecimal.valueOf(12), result.get(LoanProductConstants.NUMBER_OF_REPAYMENTS));
        assertEquals(BigDecimal.valueOf(6), result.get(LoanProductConstants.MIN_NUMBER_OF_REPAYMENTS));
        assertEquals(BigDecimal.valueOf(24), result.get(LoanProductConstants.MAX_NUMBER_OF_REPAYMENTS));
        
        // Verify that interest rate values are not affected by repayment variations
        assertEquals(BigDecimal.valueOf(12.0), result.get(LoanProductConstants.INTEREST_RATE_PER_PERIOD));
        
        // The bug would have caused MAX_INTEREST_RATE_PER_PERIOD to be set to 12 (repayment default value)
        // With the fix, it should either be null or the original value from loanProductRelatedDetail
        // Since we don't have min/max constraints set, it should be the original value
        assertEquals(BigDecimal.valueOf(12), result.get(LoanProductConstants.MAX_INTEREST_RATE_PER_PERIOD));
    }

    @Test
    public void testFetchBorrowerCycleVariationsForCycleNumber_InterestRateNotAffectedByRepaymentVariations() {
        // Create borrower cycle variations with both interest rate and repayment variations
        List<LoanProductBorrowerCycleVariations> borrowerCycleVariations = new ArrayList<>();
        
        // Add an INTEREST RATE variation for cycle 1
        LoanProductBorrowerCycleVariations interestVariation = new LoanProductBorrowerCycleVariations(
            1, // borrower cycle number
            LoanProductParamType.INTERESTRATE.getValue(), // param type
            LoanProductValueConditionType.EQUAL.getValue(), // value condition type
            BigDecimal.valueOf(10.0), // min value (min interest rate)
            BigDecimal.valueOf(15.0), // max value (max interest rate)
            BigDecimal.valueOf(12.5) // default value (default interest rate)
        );
        borrowerCycleVariations.add(interestVariation);
        
        // Add a REPAYMENT variation for cycle 1
        LoanProductBorrowerCycleVariations repaymentVariation = new LoanProductBorrowerCycleVariations(
            1, // borrower cycle number
            LoanProductParamType.REPAYMENT.getValue(), // param type
            LoanProductValueConditionType.EQUAL.getValue(), // value condition type
            BigDecimal.valueOf(6), // min value (min repayments)
            BigDecimal.valueOf(24), // max value (max repayments)
            BigDecimal.valueOf(18) // default value (default repayments)
        );
        borrowerCycleVariations.add(repaymentVariation);

        // Set the borrower cycle variations using reflection
        try {
            java.lang.reflect.Field field = LoanProduct.class.getDeclaredField("borrowerCycleVariations");
            field.setAccessible(true);
            field.set(loanProduct, borrowerCycleVariations);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set borrowerCycleVariations", e);
        }

        // Test the method
        Map<String, BigDecimal> result = loanProduct.fetchBorrowerCycleVariationsForCycleNumber(1);

        // Verify the results
        assertNotNull(result);
        
        // Verify interest rate values are set correctly from interest rate variation
        assertEquals(BigDecimal.valueOf(12.5), result.get(LoanProductConstants.INTEREST_RATE_PER_PERIOD));
        assertEquals(BigDecimal.valueOf(10.0), result.get(LoanProductConstants.MIN_INTEREST_RATE_PER_PERIOD));
        assertEquals(BigDecimal.valueOf(15.0), result.get(LoanProductConstants.MAX_INTEREST_RATE_PER_PERIOD));
        
        // Verify repayment values are set correctly from repayment variation
        assertEquals(BigDecimal.valueOf(18), result.get(LoanProductConstants.NUMBER_OF_REPAYMENTS));
        assertEquals(BigDecimal.valueOf(6), result.get(LoanProductConstants.MIN_NUMBER_OF_REPAYMENTS));
        assertEquals(BigDecimal.valueOf(24), result.get(LoanProductConstants.MAX_NUMBER_OF_REPAYMENTS));
        
        // The bug would have caused MAX_INTEREST_RATE_PER_PERIOD to be overwritten with 18 (repayment default value)
        // With the fix, it should remain 15.0 from the interest rate variation
    }
}
